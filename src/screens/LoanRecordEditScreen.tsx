import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import {useToast} from '../context/ToastContext';
import PageContainer from '../components/PageContainer';
import CustomCalendar from '../components/CustomCalendar';
import databaseService, {LoanRecord} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import {useTheme} from '../context/ThemeContext';
import ConfirmDialog from '../components/ConfirmDialog';

const LoanRecordEditScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  const {record, onSaved} = route.params || {};
  const isEditMode = !!record;
  const {showToast} = useToast();

  // 表单状态
  const [type, setType] = useState(isEditMode ? record.type : 'lend');
  const [amount, setAmount] = useState(isEditMode ? String(record.amount) : '');
  const [note, setNote] = useState(isEditMode ? record.note : '');
  const [loanDate, setLoanDate] = useState(
    isEditMode ? dayjs(record.loanDate) : dayjs(),
  );
  const [repayments, setRepayments] = useState(
    isEditMode ? record.repayments : [],
  );
  const [repayAmount, setRepayAmount] = useState('');
  const [repayDate, setRepayDate] = useState(dayjs());
  const [showLoanDateModal, setShowLoanDateModal] = useState(false);
  const [showRepayDateModal, setShowRepayDateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 保存
  const handleSave = async () => {
    if (!amount || isNaN(Number(amount))) {
      showToast('请输入有效金额', 'warning');
      return;
    }
    const data: LoanRecord = {
      id: record?.id,
      type,
      amount: Number(amount),
      note,
      loanDate: loanDate.toISOString(),
      repayments,
    };
    setLoading(true);
    try {
      if (record?.id) {
        await databaseService.updateLoanRecord(data);
      } else {
        await databaseService.addLoanRecord(data);
      }
      if (onSaved) {onSaved();}
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // 添加还款
  const handleAddRepayment = () => {
    if (!repayAmount || isNaN(Number(repayAmount))) {
      showToast('请输入有效还款金额', 'warning');
      return;
    }
    setRepayments([
      ...repayments,
      {
        amount: Number(repayAmount),
        date: repayDate.toISOString(),
      },
    ]);
    setRepayAmount('');
    setRepayDate(dayjs());
  };

  // 删除还款
  const handleDeleteRepayment = idx => {
    setRepayments(repayments.filter((_, i) => i !== idx));
  };

  // 删除借款
  const handleDelete = async () => {
    setShowDeleteDialog(false);
    if (!record?.id) {return;}
    setLoading(true);
    try {
      await databaseService.deleteLoanRecord(record.id);
      if (onSaved) {onSaved();}
      navigation.navigate('loanRecords');
    } finally {
      setLoading(false);
    }
  };

  const totalRepaid = repayments.reduce((sum, r) => sum + Number(r.amount), 0);

  // 获取类型配置
  const getTypeConfig = (type: string) => {
    return type === 'lend'
      ? { icon: 'hand-holding-dollar', color: '#34C759', bgColor: '#E8F5E8', label: '借出' }
      : { icon: 'hand-holding-heart', color: '#FF9500', bgColor: '#FFF3E0', label: '借入' };
  };

  const typeConfig = getTypeConfig(type);

  return (
    <PageContainer
      headerTitle={isEditMode ? '编辑借款记录' : '新增借款记录'}
      backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 头部卡片 */}
        <LinearGradient
          colors={[typeConfig.color, typeConfig.color + '90']}
          style={styles.headerCard}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name={typeConfig.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{typeConfig.label}记录</Text>
              <Text style={styles.headerSubtitle}>
                {amount ? `￥${amount}` : '请填写金额'}
              </Text>
            </View>
          </View>
        </LinearGradient>

        <View style={styles.formContainer}>
          {/* 类型选择卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="tags" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>借款类型</Text>
            </View>
            <View style={styles.typeBtns}>
              <TouchableOpacity
                style={[
                  styles.typeBtn,
                  type === 'lend' && [styles.typeBtnActive, {backgroundColor: '#34C759'}],
                ]}
                onPress={() => setType('lend')}>
                <Icon
                  name="hand-holding-dollar"
                  size={16}
                  color={type === 'lend' ? '#fff' : '#34C759'}
                  style={styles.typeBtnIcon}
                />
                <Text
                  style={[
                    styles.typeBtnText,
                    type === 'lend' && styles.typeBtnTextActive,
                  ]}>
                  借出
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeBtn,
                  type === 'borrow' && [styles.typeBtnActive, {backgroundColor: '#FF9500'}],
                ]}
                onPress={() => setType('borrow')}>
                <Icon
                  name="hand-holding-heart"
                  size={16}
                  color={type === 'borrow' ? '#fff' : '#FF9500'}
                  style={styles.typeBtnIcon}
                />
                <Text
                  style={[
                    styles.typeBtnText,
                    type === 'borrow' && styles.typeBtnTextActive,
                  ]}>
                  借入
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* ② 金额 */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>金额</Text>
            <TextInput
              style={styles.input}
              value={amount}
              onChangeText={setAmount}
              placeholder="请输入金额"
              keyboardType="decimal-pad"
              placeholderTextColor="#999"
            />
          </View>
          {/* ③ 备注 */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>备注</Text>
            <TextInput
              style={styles.input}
              value={note}
              onChangeText={setNote}
              placeholder="请填写借款人、借款用途等信息"
              placeholderTextColor="#999"
              multiline
            />
          </View>
          {/* ④ 借款时间 */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>借款时间</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowLoanDateModal(true)}>
              <Text style={styles.selectorText}>
                {loanDate.format('YYYY-MM-DD')}
              </Text>
              <Icon name="calendar" size={16} color={colors.primary} />
            </TouchableOpacity>
          </View>
          {/* ⑤ 还款记录 */}
          <View style={styles.formCard}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>新增还款金额</Text>
            </View>
            <View style={styles.repayRow}>
              <TextInput
                style={[styles.input, {flex: 1, backgroundColor: '#F8F8F8',marginBottom: 12}]}
                keyboardType="decimal-pad"
                value={repayAmount}
                onChangeText={setRepayAmount}
                placeholder="还款金额"
                placeholderTextColor="#999"
              />
              <TouchableOpacity
                style={[styles.selector, {backgroundColor: '#F8F8F8'}]}
                onPress={() => setShowRepayDateModal(true)}>
                <Text style={styles.selectorText}>
                  {repayDate.format('YYYY-MM-DD')}
                </Text>
                <Icon name="calendar" size={16} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.addRepayBtn, {backgroundColor: colors.primary}]}
                onPress={handleAddRepayment}>
                <Text style={{color: '#ffffff', fontSize: 16, fontWeight: 600}}>
                  新增
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {repayments.length > 0 ? (
            <View style={styles.formCard}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>还款记录</Text>
                {repayments.map((r, idx) => (
                  <View key={idx} style={styles.repayItem}>
                    <Text style={styles.repayText}>
                      {dayjs(r.date).format('YYYY-MM-DD')} 还款 ￥{r.amount}
                    </Text>
                    <TouchableOpacity
                      onPress={() => handleDeleteRepayment(idx)}>
                      <Icon name="trash" size={16} color="#FF3B30" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          ) : null}
          <View style={styles.formCard}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>还款总额</Text>
              <Text style={styles.totalRepaid}>￥{totalRepaid}</Text>
            </View>
          </View>
          <TouchableOpacity
            style={[
              styles.saveBtn,
              {backgroundColor: colors.primary, opacity: loading ? 0.7 : 1},
            ]}
            onPress={handleSave}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.saveBtnText}>
                {isEditMode ? '保存修改' : '添加借款记录'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* 借款时间选择弹窗 */}
      <Modal
        visible={showLoanDateModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowLoanDateModal(false)}>
        <TouchableWithoutFeedback onPress={() => setShowLoanDateModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, styles.dateModalContent]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择借款日期</Text>
                <TouchableOpacity
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#F2F2F7',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Icon name="xmark" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              <CustomCalendar
                initialDate={loanDate.toDate()}
                onSelectDate={date => {
                  setLoanDate(dayjs(date));
                }}
              />
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Text style={styles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Text style={styles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
      {/* 还款时间选择弹窗 */}
      <Modal
        visible={showRepayDateModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowRepayDateModal(false)}>
        <TouchableWithoutFeedback onPress={() => setShowRepayDateModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, styles.dateModalContent]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择还款日期</Text>
                <TouchableOpacity
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#F2F2F7',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Icon name="xmark" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              <CustomCalendar
                initialDate={repayDate.toDate()}
                onSelectDate={date => {
                  setRepayDate(dayjs(date));
                }}
              />
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Text style={styles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Text style={styles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* 删除借款确认弹窗 */}
      <ConfirmDialog
        visible={showDeleteDialog}
        title="删除借款记录"
        message="确定要删除该借款记录吗？此操作不可恢复。"
        onCancel={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  formContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.secondary,
    marginBottom: 8,
    paddingLeft: 2,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 0,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    // marginBottom: 12,
  },
  selector: {
    height: 44,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 0,
    borderRadius: 12,
    fontSize: 16,
    color: '#333333',
    marginBottom: 12,
  },
  selectorText: {
    fontSize: 16,
    color: '#222',
  },
  typeBtns: {flexDirection: 'row', marginLeft: 0},
  typeBtn: {
    paddingHorizontal: 18,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    marginRight: 10,
  },
  typeBtnText: {fontSize: 15, color: '#333'},
  repayRow: {marginBottom: 20},
  addRepayBtn: {
    borderRadius: 8,
    padding: 8,
    backgroundColor: '#007AFF',
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  repayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  repayText: {fontSize: 16, color: '#666'},
  totalRepaid: {fontSize: 20, color: '#34C759'},
  saveBtn: {
    marginTop: 20,
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
  },
  saveBtnText: {color: '#fff', fontSize: 17, fontWeight: '600'},
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '92%',
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  dateModalContent: {
    width: '92%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA',
  },
  cancelButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0,
    backgroundColor: '#F2F2F7',
    borderRadius: 14,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 17,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    marginLeft: 8,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
  },
});

export default LoanRecordEditScreen;
