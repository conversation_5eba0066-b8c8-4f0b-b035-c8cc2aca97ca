import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {CreditCard} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import {useToast} from '../context/ToastContext';
import {useFocusEffect} from '@react-navigation/native';
import notificationService from '../services/NotificationService';
import { useTheme } from '../context/ThemeContext';
const CreditCardListScreen = ({navigation}) => {
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [creditCards, setCreditCards] = useState<CreditCard[]>([]);

  // 当页面获得焦点时加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadCreditCards();
    }, []),
  );

  useEffect(() => {
    loadCreditCards();
  }, []);

  const loadCreditCards = async () => {
    setIsLoading(true);
    try {
      const cards = await databaseService.getAllCreditCards();
      setCreditCards(cards);
    } catch (error) {
      console.error('加载信用卡失败', error);
      showToast('加载信用卡数据失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 计算下次账单日和还款日
  const calculateNextDates = (billingDay: number, paymentDueDay: number) => {
    let now = dayjs();
    let nextBillingDate, nextPaymentDate;

    // 计算下次账单日
    if (now.date() > billingDay) {
      // 如果当前日期已经过了本月账单日，则下次账单日在下个月
      nextBillingDate = now.add(1, 'month').set('date', billingDay);
    } else {
      // 否则下次账单日就在本月
      nextBillingDate = now.set('date', billingDay);
    }

    // 计算下次还款日
    if (now.date() > paymentDueDay) {
      // 如果当前日期已经过了本月还款日，则下次还款日在下个月
      nextPaymentDate = now.add(1, 'month').set('date', paymentDueDay);
    } else {
      // 否则下次还款日就在本月
      nextPaymentDate = now.set('date', paymentDueDay);
    }

    return {
      nextBillingDate: nextBillingDate.format('MM/DD'),
      nextPaymentDate: nextPaymentDate.format('MM/DD'),
      daysUntilPayment: nextPaymentDate.diff(now, 'day'),
    };
  };

  const renderCreditCardItem = ({item}: {item: CreditCard}) => {
    const {nextBillingDate, nextPaymentDate, daysUntilPayment} = calculateNextDates(
      item.billingDay,
      item.paymentDueDay,
    );

    return (
      <TouchableOpacity
        style={styles.cardItem}
        onPress={() => navigation.navigate('creditCardDetail', {cardId: item.id})}>
        <View style={[styles.cardTop, {backgroundColor: item.color || COLORS.primary}]}>
          <Text style={styles.bankName}>{item.bankName}</Text>
          <Text style={styles.cardNumber}>**** **** **** {item.lastThreeDigits}</Text>
          <View style={styles.cardTopDates}>
            <Text style={styles.cardTopDateLabel}>账单日: {item.billingDay}</Text>
            <Text style={styles.cardTopDateLabel}>还款日: {item.paymentDueDay}</Text>
          </View>
        </View>
        <View style={styles.cardBottom}>
          <View style={styles.dateItem}>
            <Text style={styles.dateLabel}>下次账单日</Text>
            <Text style={styles.dateValue}>{nextBillingDate}</Text>
          </View>
          <View style={styles.dateItem}>
            <Text style={styles.dateLabel}>下次还款日</Text>
            <Text style={styles.dateValue}>{nextPaymentDate}</Text>
          </View>
          <View style={styles.dateItem}>
            <Text style={styles.dateLabel}>剩余天数</Text>
            <Text style={[
              styles.daysValue,
              daysUntilPayment <= 3 ? styles.urgentDays :
              daysUntilPayment <= 7 ? styles.warningDays :
              styles.normalDays,
            ]}>
              {daysUntilPayment}天
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="credit-card" size={50} color="#CCCCCC" />
      <Text style={styles.emptyText}>未保存任何信用卡信息</Text>
      <Text style={styles.emptySubText}>添加信用卡以获取账单和还款提醒</Text>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => navigation.navigate('creditCardAdd')}>
        <Text style={styles.addButtonText}>添加信用卡</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary} />
    </View>
  );

  return (
    <PageContainer headerTitle="信用卡还款提醒" backgroundColor={COLORS.background.light}>
      <View style={styles.container}>
        {isLoading ? (
          renderLoading()
        ) : (
          <>
            {creditCards.length > 0 ? (
              <FlatList
                data={creditCards}
                renderItem={renderCreditCardItem}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.listContainer}
              />
            ) : (
              renderEmptyState()
            )}
            {creditCards.length > 0 && (
              <TouchableOpacity
                style={[styles.floatingAddButton, {backgroundColor: colors.primary}]}
                onPress={() => navigation.navigate('creditCardAdd')}>
                <Icon name="plus" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            )}
            {__DEV__ && (
              <TouchableOpacity
                style={styles.testButton}
                onPress={() => {
                  notificationService.testNotification();
                  showToast('测试通知已发送', 'info');
                }}>
                <Text style={styles.testButtonText}>测试通知</Text>
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  listContainer: {
    padding: 16,
  },
  cardItem: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 5,
    marginHorizontal: 2, // 添加水平边距，让阴影效果更明显
  },
  cardTop: {
    padding: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  bankName: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.5, // iOS风格字体间距
  },
  cardNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    letterSpacing: 1, // 卡号数字间距
    marginBottom: 8, // 为下方的日期信息留出空间
  },
  cardTopDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  cardTopDateLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
  },
  cardBottom: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    padding: 20,
    paddingVertical: 16, // 调整垂直内边距
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    borderTopWidth: 0.5, // 添加微妙的分隔线
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  dateItem: {
    flex: 1,
    alignItems: 'center',
  },
  dateLabel: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '400',
    letterSpacing: -0.2, // 微调字间距
  },
  dateValue: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    letterSpacing: -0.5, // iOS风格字体间距
  },
  daysValue: {
    fontSize: 17, // 稍微增大字号，突出重要性
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  normalDays: {
    color: COLORS.text.primary,
  },
  warningDays: {
    color: '#FF9500', // iOS橙色
  },
  urgentDays: {
    color: '#FF3B30', // iOS红色
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginTop: 20,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  emptySubText: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 22, // 增加行高提高可读性
  },
  addButton: {
    backgroundColor: '#007AFF', // iOS蓝色
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.4,
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  testButton: {
    position: 'absolute',
    bottom: 94,
    right: 24,
    backgroundColor: '#FF9500', // iOS橙色
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
});

export default CreditCardListScreen;
