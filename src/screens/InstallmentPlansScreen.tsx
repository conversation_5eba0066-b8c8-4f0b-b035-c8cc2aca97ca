import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import { COLORS } from '../utils/color';
import dayjs from 'dayjs';
import { useTheme } from '../context/ThemeContext';

const InstallmentPlansScreen = ({ navigation }) => {
  const {colors} = useTheme();
  const [plans, setPlans] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // 加载分期账单
  const loadInstallmentPlans = async () => {
    try {
      setLoading(true);
      const data = await databaseService.getInstallmentPlans();
      setPlans(data);
    } catch (error) {
      console.error('加载分期账单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面获得焦点时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadInstallmentPlans();
      return () => {};
    }, [])
  );

  // 获取分期类型的图标和颜色
  const getTypeConfig = (type: string) => {
    const configs = {
      '房贷': { icon: 'house', color: '#4CAF50', bgColor: '#E8F5E8' },
      '车贷': { icon: 'car', color: '#2196F3', bgColor: '#E3F2FD' },
      '消费贷': { icon: 'shopping-cart', color: '#FF9800', bgColor: '#FFF3E0' },
      '信用卡': { icon: 'credit-card', color: '#9C27B0', bgColor: '#F3E5F5' },
      '其他': { icon: 'ellipsis-h', color: '#607D8B', bgColor: '#ECEFF1' },
    };
    return configs[type] || configs['其他'];
  };

  // 获取状态配置
  const getStatusConfig = (paidPercentage: number) => {
    if (paidPercentage === 100) {
      return { text: '已完成', color: '#4CAF50', bgColor: '#E8F5E8' };
    } else if (paidPercentage >= 50) {
      return { text: '进行中', color: '#FF9800', bgColor: '#FFF3E0' };
    } else {
      return { text: '刚开始', color: '#2196F3', bgColor: '#E3F2FD' };
    }
  };

  // 渲染列表项
  const renderPlanItem = ({ item }) => {
    const paidPercentage = item.number_of_installments > 0
      ? Math.round((item.paid_installments / item.number_of_installments) * 100)
      : 0;

    const paidAmount = item.paid_amount || 0;
    const totalInstallmentAmount = item.total_amount - (item.down_payment || 0);
    const remainingAmount = totalInstallmentAmount - paidAmount;

    const typeConfig = getTypeConfig(item.type);
    const statusConfig = getStatusConfig(paidPercentage);

    return (
      <TouchableOpacity
        style={styles.planCard}
        onPress={() => navigation.navigate('installmentPlanDetails', { planId: item.id })}
        activeOpacity={0.8}
      >
        {/* 卡片头部 */}
        <View style={styles.cardHeader}>
          <View style={styles.titleRow}>
            <View style={[styles.typeIcon, { backgroundColor: typeConfig.bgColor }]}>
              <Icon name={typeConfig.icon} size={16} color={typeConfig.color} />
            </View>
            <View style={styles.titleInfo}>
              <Text style={styles.planName} numberOfLines={1}>{item.name}</Text>
              <Text style={styles.planType}>{item.type}</Text>
            </View>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusConfig.bgColor }]}>
            <Text style={[styles.statusText, { color: statusConfig.color }]}>
              {statusConfig.text}
            </Text>
          </View>
        </View>

        {/* 金额信息 */}
        <View style={styles.amountSection}>
          <View style={styles.amountRow}>
            <View style={styles.amountItem}>
              <Text style={styles.amountLabel}>总额</Text>
              <Text style={styles.amountValue}>¥{item.total_amount.toFixed(2)}</Text>
            </View>
            <View style={styles.amountItem}>
              <Text style={styles.amountLabel}>已还</Text>
              <Text style={[styles.amountValue, { color: colors.primary }]}>
                ¥{paidAmount.toFixed(2)}
              </Text>
            </View>
            <View style={styles.amountItem}>
              <Text style={styles.amountLabel}>剩余</Text>
              <Text style={[styles.amountValue, { color: '#FF5722' }]}>
                ¥{remainingAmount.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* 进度条 */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressLabel}>还款进度</Text>
            <Text style={styles.progressPercentage}>{paidPercentage}%</Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBg}>
              <LinearGradient
                colors={[colors.primary, `${colors.primary}80`]}
                style={[styles.progressBarFill, { width: `${paidPercentage}%` }]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>
          </View>
          <Text style={styles.progressDetail}>
            {item.paid_installments || 0} / {item.number_of_installments} 期
          </Text>
        </View>

        {/* 底部信息 */}
        <View style={styles.cardFooter}>
          <View style={styles.footerItem}>
            <Icon name="calendar" size={12} color="#999" />
            <Text style={styles.footerText}>
              {dayjs(item.start_date).format('YYYY-MM-DD')}
            </Text>
          </View>
          <TouchableOpacity style={styles.detailButton}>
            <Text style={[styles.detailButtonText, { color: colors.primary }]}>查看详情</Text>
            <Icon name="chevron-right" size={12} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <PageContainer headerTitle="分期管理" backgroundColor={COLORS.secondary}>
      <View style={styles.container}>
        {loading ? (
          <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
        ) : (
          plans.length > 0 ? (
            <FlatList
              data={plans}
              renderItem={renderPlanItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.listContainer}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Icon name="credit-card" size={50} color={COLORS.text.gray} />
              <Text style={styles.emptyText}>暂无分期账单</Text>
              <Text style={styles.emptySubText}>点击下方按钮添加分期账单</Text>
            </View>
          )
        )}

        <TouchableOpacity
          style={[styles.addButton, {backgroundColor: colors.primary}]}
          onPress={() => navigation.navigate('addInstallmentPlan')}
        >
          <Icon name="plus" size={20} color="white" />
          <Text style={styles.addButtonText}>新增分期账单</Text>
        </TouchableOpacity>
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  planCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleInfo: {
    flex: 1,
  },
  planName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 2,
  },
  planType: {
    fontSize: 13,
    color: '#666666',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  amountSection: {
    marginBottom: 20,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  amountItem: {
    flex: 1,
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 4,
    fontWeight: '500',
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1A1A1A',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '700',
    color: '#1A1A1A',
  },
  progressBarContainer: {
    marginBottom: 6,
  },
  progressBarBg: {
    height: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressDetail: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#999999',
    marginLeft: 6,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailButtonText: {
    fontSize: 13,
    fontWeight: '600',
    marginRight: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: COLORS.text.gray,
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: COLORS.text.lightGray,
    marginTop: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 8,
  },
});

export default InstallmentPlansScreen;
