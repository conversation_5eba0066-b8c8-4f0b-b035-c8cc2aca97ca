import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import {useToast} from '../context/ToastContext';
import notificationService from '../services/NotificationService';
import { useTheme } from '../context/ThemeContext';

const CreditCardAddScreen = ({navigation}) => {
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [bankName, setBankName] = useState('');
  const [lastThreeDigits, setLastThreeDigits] = useState('');
  const [billingDay, setBillingDay] = useState('');
  const [paymentDueDay, setPaymentDueDay] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLORS.primary);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 卡片颜色选项 - 使用更符合iOS风格的颜色
  const colorOptions = [
    COLORS.primary,
    '#007AFF', // iOS 蓝色
    '#34C759', // iOS 绿色
    '#FF9500', // iOS 橙色
    '#FF2D55', // iOS 粉色
    '#AF52DE', // iOS 紫色
    '#5856D6', // iOS 深紫色
    '#FF3B30', // iOS 红色
  ];

  const validate = () => {
    if (!bankName.trim()) {
      showToast('请输入银行名称', 'error');
      return false;
    }

    if (!lastThreeDigits.trim() || lastThreeDigits.length !== 3 || !/^\d{3}$/.test(lastThreeDigits)) {
      showToast('请输入正确的信用卡后三位数字', 'error');
      return false;
    }

    const billDay = parseInt(billingDay);
    if (isNaN(billDay) || billDay < 1 || billDay > 31) {
      showToast('请输入有效的账单日（1-31）', 'error');
      return false;
    }

    const dueDay = parseInt(paymentDueDay);
    if (isNaN(dueDay) || dueDay < 1 || dueDay > 31) {
      showToast('请输入有效的还款日（1-31）', 'error');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validate()) {return;}

    setIsSubmitting(true);
    try {
      await databaseService.addCreditCard({
        bankName,
        lastThreeDigits,
        billingDay: parseInt(billingDay),
        paymentDueDay: parseInt(paymentDueDay),
        color: selectedColor,
      });

      // 添加成功后安排通知
      await notificationService.scheduleCreditCardReminders();

      showToast('信用卡添加成功', 'success');
      navigation.goBack();
    } catch (error) {
      console.error('添加信用卡失败', error);
      showToast('添加信用卡失败', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageContainer headerTitle="添加信用卡" backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container}>
        {/* 卡片预览 */}
        <View style={[styles.cardPreview, {backgroundColor: selectedColor}]}>
          <Text style={styles.previewBankName}>{bankName || '银行名称'}</Text>
          <Text style={styles.previewCardNumber}>
            **** **** **** {lastThreeDigits.padStart(3, '*')}
          </Text>
          <View style={styles.previewDates}>
            <Text style={styles.previewDateLabel}>账单日: {billingDay || '--'}</Text>
            <Text style={styles.previewDateLabel}>还款日: {paymentDueDay || '--'}</Text>
          </View>
        </View>

        {/* 颜色选择器 */}
        <View style={styles.colorSelector}>
          <Text style={styles.colorTitle}>选择卡片颜色</Text>
          <View style={styles.colorOptions}>
            {colorOptions.map(color => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorOption,
                  {backgroundColor: color},
                  selectedColor === color && styles.selectedColorOption,
                ]}
                onPress={() => setSelectedColor(color)}
              />
            ))}
          </View>
        </View>

        {/* 表单 */}
        <View style={styles.formContainer}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>银行名称</Text>
            <TextInput
              style={styles.input}
              placeholder="请输入银行名称"
              value={bankName}
              onChangeText={setBankName}
              maxLength={20}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>信用卡后三位</Text>
            <TextInput
              style={styles.input}
              placeholder="请输入信用卡后三位数字"
              value={lastThreeDigits}
              onChangeText={value => {
                // 只允许输入数字，且最多3位
                if (/^\d{0,3}$/.test(value)) {
                  setLastThreeDigits(value);
                }
              }}
              keyboardType="numeric"
              maxLength={3}
            />
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, {flex: 1, marginRight: 10}]}>
              <Text style={styles.label}>账单日</Text>
              <TextInput
                style={styles.input}
                placeholder="每月几号出账单"
                value={billingDay}
                onChangeText={value => {
                  // 只允许输入1-31的数字
                  if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                    setBillingDay(value);
                  }
                }}
                keyboardType="numeric"
                maxLength={2}
              />
            </View>

            <View style={[styles.formGroup, {flex: 1}]}>
              <Text style={styles.label}>还款日</Text>
              <TextInput
                style={styles.input}
                placeholder="最后还款日"
                value={paymentDueDay}
                onChangeText={value => {
                  // 只允许输入1-31的数字
                  if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                    setPaymentDueDay(value);
                  }
                }}
                keyboardType="numeric"
                maxLength={2}
              />
            </View>
          </View>

          <View style={styles.tipsContainer}>
            <Icon name="circle-info" size={14} color={COLORS.text.gray} />
            <Text style={styles.tipsText}>
              添加信用卡信息后，系统将在账单日和还款日前自动提醒您，帮助您避免逾期。
            </Text>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
        style={[styles.saveButton, isSubmitting && styles.disabledButton, {backgroundColor: colors.primary}]}
        onPress={handleSave}
        disabled={isSubmitting}>
        <Text style={styles.saveButtonText}>
          {isSubmitting ? '保存中...' : '确认添加'}
        </Text>
      </TouchableOpacity>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  cardPreview: {
    height: 200,
    borderRadius: 16,
    margin: 16,
    padding: 24,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 5,
  },
  previewBankName: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFFFFF',
    letterSpacing: -0.5, // iOS风格字体间距
  },
  previewCardNumber: {
    fontSize: 18,
    color: '#FFFFFF',
    opacity: 0.9,
    letterSpacing: 1, // 卡号数字间距
    marginTop: 8,
  },
  previewDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  previewDateLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
  },
  colorSelector: {
    margin: 16,
    marginTop: 8,
  },
  colorTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 14,
    marginBottom: 14,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3,
    elevation: 4,
  },
  formContainer: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 2,
  },
  formGroup: {
    marginBottom: 20,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  input: {
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 17,
    color: COLORS.text.primary,
  },
  tipsContainer: {
    flexDirection: 'row',
    backgroundColor: '#F2F2F7', // iOS系统浅灰色
    borderRadius: 10,
    padding: 14,
    alignItems: 'flex-start',
  },
  tipsText: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginLeft: 10,
    flex: 1,
    lineHeight: 18,
    letterSpacing: -0.2,
  },
  saveButton: {
    margin: 16,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
});

export default CreditCardAddScreen;
