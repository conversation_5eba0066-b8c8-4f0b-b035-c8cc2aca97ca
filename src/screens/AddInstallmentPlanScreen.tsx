import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import {Switch as AntdSwitch} from '@ant-design/react-native';
import {useToast} from '../context/ToastContext';
import PageContainer from '../components/PageContainer';
import CustomCalendar from '../components/CustomCalendar';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import ConfirmDialog from '../components/ConfirmDialog';
import {useTheme} from '../context/ThemeContext';
// 分期类型选项
const typeOptions = ['房贷', '车贷', '消费贷', '信用卡', '其他'];

// 分期模式选项
const modeOptions = [
  {
    value: 'equal_payment',
    label: '等额本息',
    description: '每期还款金额相同，前期利息多，后期本金多',
  },
  {
    value: 'equal_principal',
    label: '等额本金',
    description: '每期本金相同，利息递减，总利息较少',
  },
  {
    value: 'interest_first',
    label: '先息后本',
    description: '前期只还利息，最后一期还本金',
  },
  {
    value: 'no_interest',
    label: '免息分期',
    description: '无利息，平均分摊本金（如信用卡免息分期）',
  },
];

interface Plan {
  id?: number;
  name: string;
  type: string;
  start_date: string;
  total_amount: number;
  down_payment?: number;
  number_of_installments: number;
  installment_mode: string;
  annual_rate?: number; // 年利率 (%)
  handling_fee?: number; // 手续费
  auto_record: number;
}

interface PreviewData {
  first: number;
  last: number;
  average: number;
  totalInterest: number;
  totalPayment: number;
}

const AddInstallmentPlanScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  const {plan} = route.params || {};
  const isEditMode = !!plan;
  const {showToast} = useToast();

  // 表单状态
  const [name, setName] = useState(isEditMode ? plan.name : '');
  const [type, setType] = useState(isEditMode ? plan.type : typeOptions[0]);
  const [startDate, setStartDate] = useState(
    isEditMode ? plan.start_date : dayjs().format('YYYY-MM-DD'),
  );
  const [totalAmount, setTotalAmount] = useState(
    isEditMode ? plan.total_amount.toString() : '',
  );
  const [downPayment, setDownPayment] = useState(
    isEditMode ? (plan.down_payment || '0').toString() : '0',
  );
  const [installments, setInstallments] = useState(
    isEditMode ? plan.number_of_installments.toString() : '12',
  );
  const [mode, setMode] = useState(
    isEditMode ? plan.installment_mode : 'equal_payment',
  );
  const [annualRate, setAnnualRate] = useState(
    isEditMode ? (plan.annual_rate || 0).toString() : '0',
  );
  const [handlingFee, setHandlingFee] = useState(
    isEditMode ? (plan.handling_fee || 0).toString() : '0',
  );
  const [autoRecord, setAutoRecord] = useState(
    isEditMode ? plan.auto_record !== 0 : true,
  );

  // UI 状态
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState<PreviewData | null>(null);

  // 弹窗状态
  const [showTypeModal, setShowTypeModal] = useState(false);
  const [showModeModal, setShowModeModal] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);

  // 添加确认对话框状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);

  // 更新还款预览
  const updatePreview = useCallback(() => {
    if (
      !totalAmount ||
      parseFloat(totalAmount) <= 0 ||
      !installments ||
      parseInt(installments, 10) <= 0
    ) {
      setPreview(null);
      return;
    }

    const totalAmountNum = parseFloat(totalAmount);
    const downPaymentNum = parseFloat(downPayment || '0');

    if (downPaymentNum >= totalAmountNum) {
      setPreview(null);
      return;
    }

    const installmentsNum = parseInt(installments, 10);
    const amountAfterDownPayment = totalAmountNum - downPaymentNum;

    try {
      const annualRateNum = parseFloat(annualRate || '0');
      const handlingFeeNum = parseFloat(handlingFee || '0');

      const amounts = databaseService.calculateInstallmentAmount(
        amountAfterDownPayment,
        installmentsNum,
        mode,
        annualRateNum,
        handlingFeeNum,
      );

      if (!amounts || amounts.length === 0) {
        setPreview(null);
        return;
      }

      const totalPayment = amounts.reduce((sum, amount) => sum + amount, 0);
      const totalInterest = totalPayment - amountAfterDownPayment;

      setPreview({
        first: amounts[0],
        last: amounts[amounts.length - 1],
        average: totalPayment / amounts.length,
        totalInterest: Math.max(0, totalInterest),
        totalPayment,
      });
    } catch (error) {
      console.error('计算分期金额预览失败:', error);
      setPreview(null);
    }
  }, [totalAmount, downPayment, installments, mode, annualRate, handlingFee]);

  // 当关键数据变化时更新预览
  useEffect(() => {
    updatePreview();
  }, [updatePreview]);

  // 修改保存函数，添加确认流程
  const handleSave = async () => {
    // 表单验证
    if (!name.trim()) {
      showToast('请输入分期名称', 'warning');
      return;
    }

    if (!totalAmount || parseFloat(totalAmount) <= 0) {
      showToast('请输入有效的总金额', 'warning');
      return;
    }

    const totalAmountNum = parseFloat(totalAmount);
    const downPaymentNum = parseFloat(downPayment || '0');

    if (downPaymentNum >= totalAmountNum) {
      showToast('首付不能大于或等于总金额', 'warning');
      return;
    }

    const installmentsNum = parseInt(installments, 10);
    if (!installmentsNum || installmentsNum <= 0) {
      showToast('请输入有效的分期期数', 'warning');
      return;
    }

    // 如果是编辑模式，先显示确认对话框
    if (isEditMode) {
      setConfirmDialogVisible(true);
      return;
    }

    // 如果是新增模式，直接保存
    saveInstallmentPlan();
  };

  // 实际保存分期账单的函数
  const saveInstallmentPlan = async () => {
    try {
      setLoading(true);

      const planData = {
        name,
        type,
        startDate,
        totalAmount: parseFloat(totalAmount),
        downPayment: parseFloat(downPayment || '0'),
        numberOfInstallments: parseInt(installments, 10),
        installmentMode: mode,
        annualRate: parseFloat(annualRate || '0'),
        handlingFee: parseFloat(handlingFee || '0'),
        autoRecord: autoRecord ? 1 : 0,
      };

      if (isEditMode) {
        await databaseService.updateInstallmentPlan({
          id: plan.id,
          ...planData,
        });
        showToast('分期账单已更新', 'success');
      } else {
        await databaseService.addInstallmentPlan(planData);
        showToast('分期账单已添加', 'success');
      }

      navigation.goBack();
    } catch (error) {
      console.error('保存分期账单失败:', error);
      showToast('操作失败，请重试', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 渲染分期类型选择弹窗
  const renderTypeModal = () => (
    <Modal
      visible={showTypeModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowTypeModal(false)}>
      <TouchableWithoutFeedback onPress={() => setShowTypeModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择分期类型</Text>
              <TouchableOpacity onPress={() => setShowTypeModal(false)}>
                <Icon name="xmark" size={18} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={typeOptions}
              keyExtractor={item => item}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    type === item && styles.selectedOptionItem,
                  ]}
                  onPress={() => {
                    setType(item);
                    setShowTypeModal(false);
                  }}>
                  <Text
                    style={[
                      styles.optionText,
                      type === item && styles.selectedOptionText,
                    ]}>
                    {item}
                  </Text>
                  {type === item && (
                    <Icon name="check" size={16} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.optionsList}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 渲染分期模式选择弹窗
  const renderModeModal = () => (
    <Modal
      visible={showModeModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowModeModal(false)}>
      <TouchableWithoutFeedback onPress={() => setShowModeModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择分期模式</Text>
              <TouchableOpacity onPress={() => setShowModeModal(false)}>
                <Icon name="xmark" size={18} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={modeOptions}
              keyExtractor={item => item.value}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    mode === item.value && styles.selectedOptionItem,
                  ]}
                  onPress={() => {
                    setMode(item.value);
                    setShowModeModal(false);
                  }}>
                  <View style={styles.modeItemContent}>
                    <Text
                      style={[
                        styles.optionText,
                        mode === item.value && styles.selectedOptionText,
                      ]}>
                      {item.label}
                    </Text>
                    <Text style={styles.optionDescription}>
                      {item.description}
                    </Text>
                  </View>
                  {mode === item.value && (
                    <Icon name="check" size={16} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.optionsList}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 渲染日期选择弹窗
  const renderDateModal = () => (
    <Modal
      visible={showDateModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowDateModal(false)}>
      <TouchableWithoutFeedback onPress={() => setShowDateModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, styles.dateModalContent]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择开始日期</Text>
              <TouchableOpacity
                style={{
                  width: 30,
                  height: 30,
                  borderRadius: 15,
                  backgroundColor: '#F2F2F7',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => setShowDateModal(false)}>
                <Icon name="xmark" size={16} color={COLORS.text.primary} />
              </TouchableOpacity>
            </View>

            <CustomCalendar
              initialDate={new Date(startDate)}
              onSelectDate={date => {
                setStartDate(date);
              }}
            />

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowDateModal(false)}>
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  {backgroundColor: colors.primary},
                ]}
                onPress={() => setShowDateModal(false)}>
                <Text style={styles.confirmButtonText}>确定</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 获取当前选中的模式名称
  const getSelectedModeName = () => {
    const selectedMode = modeOptions.find(item => item.value === mode);
    return selectedMode ? selectedMode.label : '请选择';
  };

  // 获取分期类型的配置
  const getTypeConfig = (type: string) => {
    const configs = {
      '房贷': { icon: 'house', color: '#4CAF50', bgColor: '#E8F5E8' },
      '车贷': { icon: 'car', color: '#2196F3', bgColor: '#E3F2FD' },
      '消费贷': { icon: 'cart-shopping', color: '#FF9800', bgColor: '#FFF3E0' },
      '信用卡': { icon: 'credit-card', color: '#9C27B0', bgColor: '#F3E5F5' },
      '其他': { icon: 'ellipsis-h', color: '#607D8B', bgColor: '#ECEFF1' },
    };
    return configs[type] || configs['其他'];
  };

  const typeConfig = getTypeConfig(type);

  return (
    <PageContainer
      headerTitle={isEditMode ? '编辑分期账单' : '新增分期账单'}
      backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 头部卡片 */}
        <LinearGradient
          colors={[typeConfig.color, typeConfig.color + '90']}
          style={styles.headerCard}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name={typeConfig.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{type}分期</Text>
              <Text style={styles.headerSubtitle}>
                {name || '请填写分期名称'}
              </Text>
            </View>
          </View>
        </LinearGradient>

        <View style={styles.formContainer}>
          {/* 基本信息卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="circle-info" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>基本信息</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>分期名称</Text>
              <View style={styles.inputContainer}>
                <Icon name="tag" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="例如：iPhone分期"
                  placeholderTextColor={COLORS.text.placeholder}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>分期类型</Text>
              <TouchableOpacity
                style={styles.selector}
                onPress={() => setShowTypeModal(true)}>
                <Icon name={typeConfig.icon} size={16} color={typeConfig.color} />
                <Text style={styles.selectorText}>{type}</Text>
                <Icon name="chevron-right" size={14} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>开始日期</Text>
              <TouchableOpacity
                style={styles.selector}
                onPress={() => setShowDateModal(true)}>
                <Icon name="calendar" size={16} color={colors.primary} />
                <Text style={styles.selectorText}>
                  {dayjs(startDate).format('YYYY年MM月DD日')}
                </Text>
                <Icon name="chevron-right" size={14} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 金额信息卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="dollar-sign" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>金额信息</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>总金额</Text>
              <View style={styles.inputContainer}>
                <Icon name="dollar-sign" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={totalAmount}
                  onChangeText={setTotalAmount}
                  placeholder="输入总金额"
                  keyboardType="decimal-pad"
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>元</Text>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>首付金额 (可选)</Text>
              <View style={styles.inputContainer}>
                <Icon name="hand-holding-dollar" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={downPayment}
                  onChangeText={setDownPayment}
                  placeholder="输入首付金额，无首付请输入0"
                  keyboardType="decimal-pad"
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>元</Text>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>分期期数</Text>
              <View style={styles.inputContainer}>
                <Icon name="calendar-days" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={installments}
                  onChangeText={setInstallments}
                  placeholder="输入分期期数，例如：12"
                  keyboardType="number-pad"
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>期</Text>
              </View>
            </View>
          </View>

          {/* 分期设置卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="gear" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>分期设置</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>分期模式</Text>
              <TouchableOpacity
                style={styles.selector}
                onPress={() => setShowModeModal(true)}>
                <Icon name="calculator" size={16} color={colors.primary} />
                <Text style={styles.selectorText}>{getSelectedModeName()}</Text>
                <Icon name="chevron-right" size={14} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            {mode !== 'no_interest' && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>年利率</Text>
                <View style={styles.inputContainer}>
                  <Icon name="percent" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    value={annualRate}
                    onChangeText={setAnnualRate}
                    placeholder="输入年利率，例如：12.5"
                    keyboardType="decimal-pad"
                    placeholderTextColor={COLORS.text.placeholder}
                  />
                  <Text style={styles.inputSuffix}>%</Text>
                </View>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>手续费 (可选)</Text>
              <View style={styles.inputContainer}>
                <Icon name="receipt" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={handlingFee}
                  onChangeText={setHandlingFee}
                  placeholder="输入一次性手续费金额"
                  keyboardType="decimal-pad"
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>元</Text>
              </View>
            </View>
          </View>

          {/* 其他设置卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="sliders" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>其他设置</Text>
            </View>

            <View style={styles.switchContainer}>
              <View style={styles.switchLabelContainer}>
                <View style={styles.switchTitleRow}>
                  <Icon name="robot" size={16} color={colors.primary} />
                  <Text style={styles.switchTitle}>自动记账</Text>
                </View>
                <Text style={styles.switchDescription}>
                  开启后，更改分期还款状态时会自动添加/删除对应的交易记录
                </Text>
              </View>
              <AntdSwitch
                checked={autoRecord}
                onChange={setAutoRecord}
                color={colors.primary}
              />
            </View>
          </View>

          {/* 还款预览 */}
          {preview && (
            <View style={styles.previewContainer}>
              <Text style={styles.previewTitle}>还款预览</Text>

              <View style={styles.previewRow}>
                <View style={styles.previewItem}>
                  <Text style={styles.previewLabel}>第一期</Text>
                  <Text style={styles.previewValue}>
                    ¥{preview.first.toFixed(2)}
                  </Text>
                </View>

                <View style={styles.previewItem}>
                  <Text style={styles.previewLabel}>最后一期</Text>
                  <Text style={styles.previewValue}>
                    ¥{preview.last.toFixed(2)}
                  </Text>
                </View>

                <View style={styles.previewItem}>
                  <Text style={styles.previewLabel}>平均每期</Text>
                  <Text style={styles.previewValue}>
                    ¥{preview.average.toFixed(2)}
                  </Text>
                </View>
              </View>

              {/* 利息信息 */}
              {preview.totalInterest > 0 && (
                <View style={styles.interestInfo}>
                  <View style={styles.interestRow}>
                    <Text style={styles.interestLabel}>总利息</Text>
                    <Text style={styles.interestValue}>
                      ¥{preview.totalInterest.toFixed(2)}
                    </Text>
                  </View>
                  <View style={styles.interestRow}>
                    <Text style={styles.interestLabel}>总还款</Text>
                    <Text style={styles.interestValue}>
                      ¥{preview.totalPayment.toFixed(2)}
                    </Text>
                  </View>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            {backgroundColor: typeConfig.color, opacity: loading ? 0.7 : 1},
          ]}
          onPress={handleSave}
          disabled={loading}
          activeOpacity={0.8}>
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Icon
                name={isEditMode ? 'floppy-disk' : 'plus'}
                size={16}
                color="#FFFFFF"
                style={styles.buttonIcon}
              />
              <Text style={styles.saveButtonText}>
                {isEditMode ? '保存修改' : '添加分期账单'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* 渲染弹窗 */}
      {renderTypeModal()}
      {renderModeModal()}
      {renderDateModal()}

      {/* 添加确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="修改分期账单"
        message="修改分期账单会重置所有分期的还款状态。确定要继续吗？"
        onCancel={() => setConfirmDialogVisible(false)}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          saveInstallmentPlan();
        }}
        cancelText="取消"
        confirmText="确定"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  // 头部卡片样式
  headerCard: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
  },
  formContainer: {
    padding: 16,
    paddingTop: 8,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  cardTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.4,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 10,
    letterSpacing: -0.3,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    padding: 0,
  },
  inputSuffix: {
    fontSize: 16,
    color: COLORS.text.gray,
    fontWeight: '500',
    marginLeft: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  selectorText: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    marginLeft: 12,
    fontWeight: '500',
  },
  previewContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 14,
    padding: 20,
    marginTop: 24,
    marginBottom: 30,
  },
  previewTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewItem: {
    flex: 1,
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 6,
  },
  previewValue: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  buttonContainer: {
    padding: 16,
    paddingBottom: 32,
    backgroundColor: 'transparent',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: COLORS.background.white,
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  dateModalContent: {
    width: '92%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  optionsList: {
    paddingVertical: 8,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingVertical: 18,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  selectedOptionItem: {
    backgroundColor: 'rgba(0, 122, 255, 0.07)',
  },
  optionText: {
    fontSize: 17,
    color: COLORS.text.primary,
  },
  selectedOptionText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  optionDescription: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginTop: 4,
  },
  modeItemContent: {
    flex: 1,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA',
  },
  cancelButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0,
    backgroundColor: '#F2F2F7',
    borderRadius: 14,
    marginRight: 8,
  },
  cancelButtonText: {
    color: COLORS.text.primary,
    fontSize: 17,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    marginLeft: 8,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 4,
  },
  switchLabelContainer: {
    flex: 1,
    marginRight: 16,
  },
  switchTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  switchTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.3,
  },
  switchDescription: {
    fontSize: 13,
    color: COLORS.text.gray,
    lineHeight: 18,
    letterSpacing: -0.2,
  },
  interestInfo: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  interestRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  interestLabel: {
    fontSize: 14,
    color: COLORS.text.gray,
  },
  interestValue: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
});

export default AddInstallmentPlanScreen;
