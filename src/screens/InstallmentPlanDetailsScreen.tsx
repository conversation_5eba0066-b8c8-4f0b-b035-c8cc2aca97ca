import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import { useToast } from '../context/ToastContext';
import PageContainer from '../components/PageContainer';
import ConfirmDialog from '../components/ConfirmDialog';
import databaseService from '../services/DatabaseService';
import { COLORS } from '../utils/color';
import dayjs from 'dayjs';
import { useTheme } from '../context/ThemeContext';
interface InstallmentPlan {
  id: number;
  name: string;
  type: string;
  start_date: string;
  total_amount: number;
  down_payment: number;
  number_of_installments: number;
  installment_mode: string;
  auto_record: number;
  installments: Array<{
    id: number;
    plan_id: number;
    installment_number: number;
    due_date: string;
    amount: number;
    is_paid: number;
    payment_date: string | null;
    transaction_id: number | null;
  }>;
}

const InstallmentPlanDetailsScreen = ({ route, navigation }) => {
  const { planId } = route.params;
  const { showToast } = useToast();
  const {colors, themeConfig} = useTheme();
  const [plan, setPlan] = useState<InstallmentPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(() => {});

  // 获取分期类型的图标和颜色配置
  const getTypeConfig = (type: string) => {
    const configs = {
      '房贷': { icon: 'house', color: '#4CAF50', bgColor: '#E8F5E8' },
      '车贷': { icon: 'car', color: '#2196F3', bgColor: '#E3F2FD' },
      '消费贷': { icon: 'shopping-cart', color: '#FF9800', bgColor: '#FFF3E0' },
      '信用卡': { icon: 'credit-card', color: '#9C27B0', bgColor: '#F3E5F5' },
      '其他': { icon: 'ellipsis-h', color: '#607D8B', bgColor: '#ECEFF1' },
    };
    return configs[type] || configs['其他'];
  };

  // 获取分期模式的显示名称
  const getModeDisplayName = (mode: string) => {
    const modeNames = {
      'equal_payment': '等额本息',
      'equal_principal': '等额本金',
      'interest_first': '先息后本',
      'no_interest': '免息分期',
      'equal': '等额本金', // 兼容旧版本
      'decreasing': '递减还款',
      'increasing': '递增还款',
    };
    return modeNames[mode] || mode;
  };

  // 加载分期账单详情
  const loadPlanDetails = useCallback(async () => {
    try {
      setLoading(true);
      const planDetails = await databaseService.getInstallmentPlanDetails(planId);
      if (planDetails) {
        setPlan(planDetails);
      } else {
        showToast('未找到分期账单', 'error');
        navigation.goBack();
      }
    } catch (error) {
      console.error('加载分期账单详情失败:', error);
      showToast('加载分期账单详情失败', 'error');
    } finally {
      setLoading(false);
    }
  }, [planId, navigation, showToast]);

  // 使用 useFocusEffect 监听页面每次获得焦点时重新加载数据
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;

      const loadData = async () => {
        try {
          if (isMounted) {setLoading(true);}
          const planDetails = await databaseService.getInstallmentPlanDetails(planId);

          if (isMounted) {
            if (planDetails) {
              setPlan(planDetails);
            } else {
              showToast('未找到分期账单', 'error');
              navigation.goBack();
            }
          }
        } catch (error) {
          if (isMounted) {
            console.error('加载分期账单详情失败:', error);
            showToast('加载分期账单详情失败', 'error');
          }
        } finally {
          if (isMounted) {
            setLoading(false);
          }
        }
      };

      loadData();

      return () => {
        isMounted = false;
      };
    }, [planId, navigation, showToast])
  );

  // 处理删除分期账单
  const handleDeletePlan = () => {
    setConfirmDialogMessage('确定要删除此分期账单吗？此操作不可恢复。');
    setConfirmDialogAction(() => async () => {
      try {
        await databaseService.deleteInstallmentPlan(planId);
        showToast('分期账单已删除', 'success');
        navigation.goBack();
      } catch (error) {
        console.error('删除分期账单失败:', error);
        showToast('删除分期账单失败', 'error');
      }
    });
    setConfirmDialogVisible(true);
  };

  // 处理编辑分期账单
  const handleEditPlan = () => {
    navigation.navigate('addInstallmentPlan', { plan });
  };

  // 处理还款状态切换
  const togglePaymentStatus = async (installmentDetail) => {
    try {
      const newPaidStatus = !installmentDetail.is_paid;
      const today = dayjs().format('YYYY-MM-DD'); // 当前日期

      // 如果开启了自动记账功能
      if (plan?.auto_record === 1) {
        if (newPaidStatus) {
          // 查找或创建分期类型对应的账单类别
          let categoryId = '0';
          let categoryName = plan.type;

          // 先查找是否已存在该名称的支出类别
          const allCategories = await databaseService.getAllCategories();
          const existingCategory = allCategories.find(
            c => c.name === plan.type && c.isExpense === true
          );

          if (existingCategory) {
            // 如果已存在该类别，使用它的ID
            categoryId = existingCategory.id;
          } else {
            // 如果不存在，创建新类别
            try {
              // 创建一个新的分期类型账单类别
              const newCategoryId = await databaseService.addCategory({
                name: plan.type,
                isExpense: true, // 分期付款总是支出
                icon: 'money-bill', // 使用钱币图标
              });

              categoryId = newCategoryId;
              showToast(`已创建"${plan.type}"分类`, 'success');
            } catch (categoryError) {
              console.error('创建分期类别失败:', categoryError);
              // 如果创建失败，使用默认类别
            }
          }

          // 标记为已付款，创建交易记录
          const transactionId = await databaseService.addTransaction({
            type: 'expense',
            amount: installmentDetail.amount,
            note: `${plan.name} - 第${installmentDetail.installment_number}期还款`,
            date: today,
            categoryId,
            categoryName,
          });

          // 更新分期详情，添加关联的交易ID和付款日期
          await databaseService.updateInstallmentPaymentStatus(
            installmentDetail.id,
            newPaidStatus,
            today,
            parseInt(transactionId, 10) // 确保转换为数字类型
          );

          showToast('已标记为已付款并自动记账', 'success');
        } else {
          // 标记为未付款，删除关联的交易记录
          if (installmentDetail.transaction_id) {
            await databaseService.deleteTransaction(installmentDetail.transaction_id);
          }

          // 更新分期详情，清除关联的交易ID和付款日期
          await databaseService.updateInstallmentPaymentStatus(
            installmentDetail.id,
            newPaidStatus,
            null,
            null
          );

          showToast('已标记为未付款并删除对应记账', 'success');
        }
      } else {
        // 未开启自动记账，只更新付款状态
        await databaseService.updateInstallmentPaymentStatus(
          installmentDetail.id,
          newPaidStatus,
          newPaidStatus ? today : null
        );

        showToast(
          newPaidStatus ? '已标记为已付款' : '已标记为未付款',
          'success'
        );
      }

      // 重新加载数据以更新UI
      loadPlanDetails();
    } catch (error) {
      console.error('更新还款状态失败:', error);
      showToast('更新还款状态失败', 'error');
    }
  };

  // 渲染分期详情信息
  const renderPlanInfo = () => {
    if (!plan) {return null;}

    // 计算已还款和未还款金额
    const totalInstallmentAmount = plan.total_amount - (plan.down_payment || 0);
    const paidAmount = plan.installments
      .filter(item => item.is_paid)
      .reduce((sum, item) => sum + item.amount, 0);
    const remainingAmount = totalInstallmentAmount - paidAmount;

    // 计算进度百分比
    const progressPercentage = Math.round(
      (paidAmount / totalInstallmentAmount) * 100
    );

    const typeConfig = getTypeConfig(plan.type);

    return (
      <View style={styles.infoContainer}>
        {/* 头部信息 */}
        <LinearGradient
          colors={[colors.primary, `${colors.primary}80`]}
          style={styles.headerGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.headerContent}>
            <View style={styles.titleSection}>
              <View style={[styles.typeIconLarge, { backgroundColor: typeConfig.bgColor }]}>
                <Icon name={typeConfig.icon} size={24} color={typeConfig.color} />
              </View>
              <View style={styles.titleInfo}>
                <Text style={styles.planNameLarge}>{plan.name}</Text>
                <Text style={styles.planTypeLarge}>{plan.type}</Text>
              </View>
            </View>
            <View style={styles.progressBadge}>
              <Text style={styles.progressBadgeText}>{progressPercentage}%</Text>
            </View>
          </View>
        </LinearGradient>

        {/* 金额统计卡片 */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>总金额</Text>
            <Text style={styles.statValue}>¥{plan.total_amount.toFixed(2)}</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statLabel}>已还</Text>
            <Text style={[styles.statValue, { color: '#4CAF50' }]}>
              ¥{paidAmount.toFixed(2)}
            </Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statLabel}>剩余</Text>
            <Text style={[styles.statValue, { color: '#FF5722' }]}>
              ¥{remainingAmount.toFixed(2)}
            </Text>
          </View>
        </View>

        {/* 进度条 */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>还款进度</Text>
            <Text style={styles.progressDetail}>
              {plan.installments.filter(item => item.is_paid).length} / {plan.number_of_installments} 期
            </Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBg}>
              <LinearGradient
                colors={[colors.primary, `${colors.primary}80`]}
                style={[styles.progressBarFill, { width: `${progressPercentage}%` }]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>
          </View>
        </View>

        {/* 详细信息 */}
        <View style={styles.detailsGrid}>
          <View style={styles.detailCard}>
            <Icon name="calendar" size={16} color={colors.primary} />
            <Text style={styles.detailCardLabel}>开始日期</Text>
            <Text style={styles.detailCardValue}>
              {dayjs(plan.start_date).format('YYYY-MM-DD')}
            </Text>
          </View>

          <View style={styles.detailCard}>
            <Icon name="list-ol" size={16} color={colors.primary} />
            <Text style={styles.detailCardLabel}>分期期数</Text>
            <Text style={styles.detailCardValue}>{plan.number_of_installments}期</Text>
          </View>

          <View style={styles.detailCard}>
            <Icon name="chart-line" size={16} color={colors.primary} />
            <Text style={styles.detailCardLabel}>还款模式</Text>
            <Text style={styles.detailCardValue}>
              {getModeDisplayName(plan.installment_mode)}
            </Text>
          </View>

          {plan.down_payment > 0 && (
            <View style={styles.detailCard}>
              <Icon name="hand-holding-dollar" size={16} color={colors.primary} />
              <Text style={styles.detailCardLabel}>首付金额</Text>
              <Text style={styles.detailCardValue}>¥{plan.down_payment.toFixed(2)}</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染分期列表
  const renderInstallments = () => {
    if (!plan || !plan.installments || plan.installments.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>暂无分期数据</Text>
        </View>
      );
    }

    return (
      <View style={styles.installmentsContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>分期明细</Text>
          <Text style={styles.sectionSubtitle}>
            共 {plan.installments.length} 期，已完成 {plan.installments.filter(item => item.is_paid).length} 期
          </Text>
        </View>

        {plan.installments.map((installment, index) => {
          const isPaid = installment.is_paid === 1;
          const isDueDate = dayjs().isAfter(installment.due_date) && !isPaid;
          const isUpcoming = dayjs().add(7, 'day').isAfter(installment.due_date) && !isPaid && !isDueDate;

          return (
            <TouchableOpacity
              key={installment.id}
              style={[
                styles.installmentCard,
                isPaid && styles.paidInstallmentCard,
                isDueDate && styles.overdueInstallmentCard,
                isUpcoming && styles.upcomingInstallmentCard,
              ]}
              onPress={() => togglePaymentStatus(installment)}
              activeOpacity={0.8}
            >
              <View style={styles.installmentLeft}>
                <View style={[
                  styles.installmentNumberBadge,
                  isPaid && { backgroundColor: '#4CAF50' },
                  isDueDate && { backgroundColor: '#FF5722' },
                  isUpcoming && { backgroundColor: '#FF9800' },
                  !isPaid && !isDueDate && !isUpcoming && { backgroundColor: colors.primary }
                ]}>
                  <Text style={styles.installmentNumberText}>
                    {installment.installment_number}
                  </Text>
                </View>
                <View style={styles.installmentInfo}>
                  <Text style={styles.installmentAmount}>
                    ¥{installment.amount.toFixed(2)}
                  </Text>
                  <Text style={styles.installmentDate}>
                    {dayjs(installment.due_date).format('MM-DD')}
                  </Text>
                </View>
              </View>

              <View style={styles.installmentRight}>
                {isPaid ? (
                  <View style={styles.statusBadge}>
                    <Icon name="check" size={12} color="#FFFFFF" />
                    <Text style={styles.statusBadgeText}>已还</Text>
                  </View>
                ) : isDueDate ? (
                  <View style={[styles.statusBadge, { backgroundColor: '#FF5722' }]}>
                    <Icon name="exclamation" size={12} color="#FFFFFF" />
                    <Text style={styles.statusBadgeText}>逾期</Text>
                  </View>
                ) : isUpcoming ? (
                  <View style={[styles.statusBadge, { backgroundColor: '#FF9800' }]}>
                    <Icon name="clock" size={12} color="#FFFFFF" />
                    <Text style={styles.statusBadgeText}>即将到期</Text>
                  </View>
                ) : (
                  <View style={[styles.statusBadge, { backgroundColor: '#999999' }]}>
                    <Icon name="circle" size={12} color="#FFFFFF" />
                    <Text style={styles.statusBadgeText}>待还</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  // 渲染右上角按钮
  const renderRightComponent = () => (
    <View style={styles.headerButtons}>
      <TouchableOpacity
        style={styles.headerButton}
        onPress={handleEditPlan}
      >
        <Icon name="edit" size={18} color={colors.primary} />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.headerButton, styles.deleteButton]}
        onPress={handleDeletePlan}
      >
        <Icon name="trash" size={18} color={COLORS.functional.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <PageContainer
      headerTitle="分期详情"
      rightComponent={loading ? null : renderRightComponent()}
      backgroundColor={COLORS.secondary}
    >
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <ScrollView style={styles.container}>
          {renderPlanInfo()}
          {renderInstallments()}
        </ScrollView>
      )}

      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onCancel={() => setConfirmDialogVisible(false)}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          if (confirmDialogAction) {
            confirmDialogAction();
          }
        }}
        cancelText="取消"
        confirmText="确定"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    margin: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  headerGradient: {
    padding: 20,
    paddingBottom: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIconLarge: {
    width: 50,
    height: 50,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  titleInfo: {
    flex: 1,
  },
  planNameLarge: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  planTypeLarge: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  progressBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  progressBadgeText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  statLabel: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 6,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A1A1A',
  },
  progressSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  progressDetail: {
    fontSize: 14,
    color: '#666666',
  },
  progressBarContainer: {
    marginBottom: 8,
  },
  progressBarBg: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingBottom: 20,
    justifyContent: 'space-between',
  },
  detailCard: {
    width: '48%',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  detailCardLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  detailCardValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A1A1A',
    textAlign: 'center',
  },
  installmentsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    margin: 16,
    marginTop: 0,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 32,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  installmentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  paidInstallmentCard: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  overdueInstallmentCard: {
    backgroundColor: '#FFEBEE',
    borderColor: '#FF5722',
  },
  upcomingInstallmentCard: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
  },
  installmentLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  installmentNumberBadge: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  installmentNumberText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '700',
  },
  installmentInfo: {
    flex: 1,
  },
  installmentAmount: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 2,
  },
  installmentDate: {
    fontSize: 13,
    color: '#666666',
  },
  installmentRight: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 4,
  },

  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.text.gray,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  deleteButton: {
    marginLeft: 10,
  },
});

export default InstallmentPlanDetailsScreen;
