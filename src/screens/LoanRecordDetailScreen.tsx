import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import PageContainer from '../components/PageContainer';
import ConfirmDialog from '../components/ConfirmDialog';
import dayjs from 'dayjs';
import databaseService, {LoanRecord} from '../services/DatabaseService';

const LoanRecordDetailScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  const {id} = route.params || {};
  const [record, setRecord] = useState<LoanRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    if (id) {
      loadDetail();
    }
  }, [id]);

  const loadDetail = async () => {
    setLoading(true);
    const data = await databaseService.getLoanRecordById(id);
    setRecord(data);
    setLoading(false);
  };

  // 删除借款
  const handleDelete = async () => {
    setShowDeleteDialog(false);
    if (!record?.id) return;
    setLoading(true);
    try {
      await databaseService.deleteLoanRecord(record.id);
      loadDetail();
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <PageContainer
        headerTitle="借款详情"
        showBackButton
        backgroundColor={colors.primary}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </PageContainer>
    );
  }

  if (!record) {
    return (
      <PageContainer
        headerTitle="借款详情"
        showBackButton
        backgroundColor={colors.primary}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Text>无数据</Text>
        </View>
      </PageContainer>
    );
  }

  const totalRepaid =
    record.repayments?.reduce((sum, r) => sum + Number(r.amount), 0) || 0;

  // 还款差额
  const totalAmount = record.amount - totalRepaid;

  return (
    <PageContainer
      headerTitle="借款详情"
      showBackButton
      backgroundColor={COLORS.secondary}>
      <ScrollView style={styles.container}>
        <View style={styles.options}>
          {/* 编辑按钮 */}
          <TouchableOpacity
            style={styles.editBtn}
            onPress={() =>
              navigation.navigate('loanRecordEdit', {
                record,
                onSaved: () => loadDetail(),
              })
            }>
            <Icon name="pen-to-square" size={18} color="#007AFF" />
            <Text style={styles.editBtnText}>编辑</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.editBtn}
            onPress={() => setShowDeleteDialog(true)}>
            <Icon name="trash" size={18} color="#FF3B30" />
            <Text style={[styles.editBtnText, {color: '#FF3B30'}]}>删除</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.card}>
          <View style={styles.row}>
            <View
              style={[
                styles.iconCircle,
                {
                  backgroundColor:
                    record.type === 'lend' ? '#FFF4E5' : '#E9F9F1',
                },
              ]}>
              <Icon
                name={record.type === 'lend' ? 'arrow-up' : 'arrow-down'}
                size={22}
                color={record.type === 'lend' ? '#FF9500' : '#34C759'}
              />
            </View>
            <Text style={styles.typeText}>
              {record.type === 'lend' ? '借出' : '借入'}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>金额：</Text>
            <Text style={styles.value}>￥{record.amount}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>借款时间：</Text>
            <Text style={styles.value}>
              {dayjs(record.loanDate).format('YYYY-MM-DD')}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>备注：</Text>
            <Text style={styles.value}>{record.note || '-'}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>还款总额：</Text>
            <Text style={styles.value}>￥{totalRepaid}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>还款差额：</Text>
            <Text style={styles.value}>￥{totalAmount}</Text>
          </View>
        </View>
        <View style={styles.repayCard}>
          <Text style={styles.repayTitle}>还款记录</Text>
          <View style={styles.repayList}>
            {record.repayments?.length ? (
              record.repayments.map((r, idx) => (
                <View key={idx} style={styles.repayItem}>
                  <Text style={styles.repayText}>
                    {dayjs(r.date).format('YYYY-MM-DD')} 还款 ￥{r.amount}
                  </Text>
                </View>
              ))
            ) : (
              <Text style={styles.value}>暂无还款记录</Text>
            )}
          </View>
        </View>
      </ScrollView>
      {/* 删除借款确认弹窗 */}
      <ConfirmDialog
        visible={showDeleteDialog}
        title="删除借款记录"
        message="确定要删除该借款记录吗？此操作不可恢复。"
        onCancel={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#F6F7FB', padding: 16},
  options: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 18,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 2},
    elevation: 2,
  },
  row: {flexDirection: 'row', alignItems: 'center', marginBottom: 18},
  iconCircle: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  label: {fontSize: 15, color: '#333', fontWeight: '500'},
  value: {fontSize: 15, color: '#333', marginLeft: 10},
  typeText: {fontSize: 16, color: '#333', marginLeft: 10, fontWeight: '600'},
  repayCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 18,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 2},
    elevation: 2,
  },
  repayTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  repayList: {marginBottom: 10},
  repayItem: {paddingVertical: 4},
  repayText: {fontSize: 14, color: '#666'},
  editBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginRight: 8,
    marginBottom: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  editBtnText: {
    color: '#007AFF',
    fontSize: 15,
    marginLeft: 6,
    fontWeight: '500',
  },
});

export default LoanRecordDetailScreen;
