import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import PageContainer from '../components/PageContainer';
import ConfirmDialog from '../components/ConfirmDialog';
import dayjs from 'dayjs';
import databaseService, {LoanRecord} from '../services/DatabaseService';

const LoanRecordDetailScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  const {id} = route.params || {};
  const [record, setRecord] = useState<LoanRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    if (id) {
      loadDetail();
    }
  }, [id]);

  const loadDetail = async () => {
    setLoading(true);
    const data = await databaseService.getLoanRecordById(id);
    setRecord(data);
    setLoading(false);
  };

  // 删除借款
  const handleDelete = async () => {
    setShowDeleteDialog(false);
    if (!record?.id) return;
    setLoading(true);
    try {
      await databaseService.deleteLoanRecord(record.id);
      loadDetail();
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <PageContainer
        headerTitle="借款详情"
        showBackButton
        backgroundColor={colors.primary}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </PageContainer>
    );
  }

  if (!record) {
    return (
      <PageContainer
        headerTitle="借款详情"
        showBackButton
        backgroundColor={colors.primary}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Text>无数据</Text>
        </View>
      </PageContainer>
    );
  }

  const totalRepaid =
    record.repayments?.reduce((sum, r) => sum + Number(r.amount), 0) || 0;

  // 还款差额
  const remainingAmount = Number(record.amount) - totalRepaid;
  const isFullyPaid = remainingAmount <= 0;

  // 获取类型配置
  const getTypeConfig = (type: string) => {
    return type === 'lend'
      ? {
          icon: 'hand-holding-dollar',
          color: '#34C759',
          bgColor: '#E8F5E8',
          label: '借出',
          gradientColors: ['#34C759', '#28A745']
        }
      : {
          icon: 'hand-holding-heart',
          color: '#FF9500',
          bgColor: '#FFF3E0',
          label: '借入',
          gradientColors: ['#FF9500', '#FF8C00']
        };
  };

  const typeConfig = getTypeConfig(record.type);

  return (
    <PageContainer
      headerTitle="借款详情"
      showBackButton
      backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 头部卡片 */}
        <LinearGradient
          colors={typeConfig.gradientColors}
          style={styles.headerCard}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name={typeConfig.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{typeConfig.label}记录</Text>
              <Text style={styles.headerSubtitle}>￥{record.amount}</Text>
            </View>
            {isFullyPaid && (
              <View style={styles.paidBadge}>
                <Icon name="check-circle" size={16} color="#FFFFFF" />
                <Text style={styles.paidText}>已还清</Text>
              </View>
            )}
          </View>
        </LinearGradient>
        {/* 基本信息卡片 */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Icon name="circle-info" size={16} color={colors.primary} />
            <Text style={styles.cardTitle}>基本信息</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.label}>借款时间</Text>
            <Text style={styles.value}>
              {dayjs(record.loanDate).format('YYYY年MM月DD日')}
            </Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.label}>备注信息</Text>
            <Text style={styles.value}>{record.note || '无备注'}</Text>
          </View>
        </View>

        {/* 统计信息卡片 */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Icon name="chart-line" size={16} color={colors.primary} />
            <Text style={styles.cardTitle}>统计信息</Text>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>借款金额</Text>
              <Text style={[styles.statValue, {color: typeConfig.color}]}>
                ￥{record.amount}
              </Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>已还金额</Text>
              <Text style={[styles.statValue, {color: '#34C759'}]}>
                ￥{totalRepaid}
              </Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>剩余金额</Text>
              <Text style={[styles.statValue, {color: isFullyPaid ? '#34C759' : '#FF9500'}]}>
                ￥{remainingAmount.toFixed(2)}
              </Text>
            </View>
          </View>

          {/* 进度条 */}
          <View style={styles.progressSection}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressLabel}>还款进度</Text>
              <Text style={styles.progressPercent}>
                {((totalRepaid / Number(record.amount)) * 100).toFixed(0)}%
              </Text>
            </View>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${Math.min((totalRepaid / Number(record.amount)) * 100, 100)}%`,
                    backgroundColor: isFullyPaid ? '#34C759' : typeConfig.color
                  }
                ]}
              />
            </View>
          </View>
        </View>
        {/* 还款记录卡片 */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Icon name="list-check" size={16} color={colors.primary} />
            <Text style={styles.cardTitle}>还款记录</Text>
          </View>

          {record.repayments?.length ? (
            <View style={styles.repaymentsList}>
              {record.repayments.map((r, idx) => (
                <View key={idx} style={styles.repayItem}>
                  <View style={styles.repayItemLeft}>
                    <Icon name="calendar-check" size={14} color="#34C759" />
                    <View style={styles.repayItemInfo}>
                      <Text style={styles.repayAmount}>￥{r.amount}</Text>
                      <Text style={styles.repayDate}>
                        {dayjs(r.date).format('MM月DD日')}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.repayItemRight}>
                    <Icon name="check-circle" size={16} color="#34C759" />
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyRepayments}>
              <Icon name="calendar-xmark" size={32} color="#E5E5EA" />
              <Text style={styles.emptyText}>暂无还款记录</Text>
            </View>
          )}
        </View>

        {/* 操作按钮 */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => setShowDeleteDialog(true)}
            activeOpacity={0.8}>
            <Icon name="trash" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.deleteButtonText}>删除记录</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.editButton, {backgroundColor: colors.primary}]}
            onPress={() =>
              navigation.navigate('loanRecordEdit', {
                record,
                onSaved: () => loadDetail(),
              })
            }
            activeOpacity={0.8}>
            <Icon name="pen-to-square" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.editButtonText}>编辑记录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {/* 删除借款确认弹窗 */}
      <ConfirmDialog
        visible={showDeleteDialog}
        title="删除借款记录"
        message="确定要删除该借款记录吗？此操作不可恢复。"
        onCancel={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  // 头部卡片样式
  headerCard: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: '700',
    letterSpacing: -0.6,
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  paidText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 4,
    letterSpacing: -0.1,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  cardTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.4,
  },
  // 信息项样式
  infoItem: {
    marginBottom: 16,
  },
  label: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '500',
    letterSpacing: -0.2,
  },
  value: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  // 统计信息样式
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '400',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 16,
  },
  // 进度条样式
  progressSection: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    letterSpacing: -0.3,
  },
  progressPercent: {
    fontSize: 15,
    fontWeight: '700',
    color: COLORS.text.primary,
    letterSpacing: -0.3,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  editBtnText: {
    color: '#007AFF',
    fontSize: 15,
    marginLeft: 6,
    fontWeight: '500',
  },
  // 还款记录样式
  repaymentsList: {
    gap: 12,
  },
  repayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  repayItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  repayItemInfo: {
    marginLeft: 10,
    flex: 1,
  },
  repayAmount: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '600',
    marginBottom: 2,
  },
  repayDate: {
    fontSize: 13,
    color: COLORS.text.gray,
    fontWeight: '400',
  },
  repayItemRight: {
    marginLeft: 12,
  },
  emptyRepayments: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginTop: 12,
    fontWeight: '500',
  },
  // 操作按钮样式
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginHorizontal: 16,
    marginBottom: 32,
  },
  deleteButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    backgroundColor: '#FF3B30',
    shadowColor: '#FF3B30',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  editButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export default LoanRecordDetailScreen;
