import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {LoanRecord} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import dayjs from 'dayjs';

const LoanRecordsScreen = ({navigation}) => {
  const {colors} = useTheme();
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState<LoanRecord[]>([]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadRecords();
    });
    loadRecords();
    return unsubscribe;
  }, [navigation]);

  const loadRecords = async () => {
    setLoading(true);
    try {
      const data = await databaseService.getAllLoanRecords();
      setRecords(data || []);
    } catch (e) {
      setRecords([]);
    }
    setLoading(false);
  };

  const renderItem = ({item}) => {
    const totalRepaid =
      item.repayments?.reduce((sum, r) => sum + Number(r.amount), 0) || 0;
    return (
      <TouchableOpacity
        style={styles.item}
        activeOpacity={0.8}
        onPress={() => navigation.navigate('loanRecordDetail', {id: item.id})}>
        <View style={styles.itemLeft}>
          <View
            style={[
              styles.iconCircle,
              {backgroundColor: item.type === 'lend' ? '#FFF4E5' : '#E9F9F1'},
            ]}>
            <Icon
              name={item.type === 'lend' ? 'arrow-up' : 'arrow-down'}
              size={20}
              color={item.type === 'lend' ? '#FF9500' : '#34C759'}
            />
          </View>
          <View style={{marginLeft: 12}}>
            <Text style={styles.itemTitle}>
              {item.type === 'lend' ? '借出' : '借入'} ￥{item.amount}
            </Text>
            <Text style={styles.itemNote} numberOfLines={1}>
              {item.note}
            </Text>
          </View>
        </View>
        <View style={{alignItems: 'flex-end'}}>
          <Text style={styles.itemDate}>
            {dayjs(item.loanDate).format('YYYY-MM-DD')}
          </Text>
          <Text style={styles.itemStatus}>已还：￥{totalRepaid}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <PageContainer headerTitle="借款记录" backgroundColor={COLORS.secondary}>
      <View style={styles.container}>
        {loading ? (
          <ActivityIndicator
            size="large"
            color={colors.primary}
            style={styles.loader}
          />
        ) : (
          <FlatList
            data={records}
            renderItem={renderItem}
            keyExtractor={item =>
              item.id?.toString() || Math.random().toString()
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="money-check-dollar" size={60} color="#CCCCCC" />
                <Text style={styles.emptyText}>暂无借款记录</Text>
                <Text style={styles.emptySubText}>
                  点击下方按钮添加借款记录
                </Text>
              </View>
            }
            contentContainerStyle={records.length === 0 && styles.emptyList}
          />
        )}
        <TouchableOpacity
          style={[styles.addBtn, {backgroundColor: colors.primary}]}
          activeOpacity={0.85}
          onPress={() =>
            navigation.navigate('loanRecordEdit', {onSaved: loadRecords})
          }>
          <Icon name="plus" size={22} color="#fff" />
        </TouchableOpacity>
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.gray,
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginTop: 8,
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 18,
    marginHorizontal: 14,
    marginTop: 14,
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 2},
    elevation: 2,
  },
  iconCircle: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemLeft: {flexDirection: 'row', alignItems: 'center'},
  itemTitle: {fontSize: 16, fontWeight: '600', color: '#333'},
  itemNote: {fontSize: 13, color: '#888', marginTop: 4, maxWidth: 180},
  itemDate: {fontSize: 13, color: '#999', textAlign: 'right'},
  itemStatus: {
    fontSize: 13,
    color: '#34C759',
    textAlign: 'right',
    marginTop: 2,
  },
  addBtn: {
    position: 'absolute',
    right: 24,
    bottom: 32,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 4},
  },
});

export default LoanRecordsScreen;
